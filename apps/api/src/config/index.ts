export const appConfig = {
  jwt: {
    secret: process.env.JWT_SECRET ?? 'dev-secret',
    expiresIn: '15m',
  },
  apiSurfaces: {
    internalHosts: (process.env.INTERNAL_API_HOSTS || '')
      .split(',')
      .map((h) => h.trim().toLowerCase())
      .filter(Boolean),
    externalHosts: (process.env.EXTERNAL_API_HOSTS || '')
      .split(',')
      .map((h) => h.trim().toLowerCase())
      .filter(Boolean),
  },
  stripe: {
    secretKey: process.env.STRIPE_SECRET_KEY,
    webhookSecret: process.env.STRIPE_WEBHOOK_SECRET,
    apiVersion: '2025-05-28.basil' as const,
  },
};
