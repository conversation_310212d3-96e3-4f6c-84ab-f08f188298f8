import { Test, TestingModule } from '@nestjs/testing';
import { StripeWebhookService } from './stripe-webhook.service';
import { SubscriptionRepository } from '@askinfosec/database-drizzle';

// Mock environment variables
process.env.STRIPE_SECRET_KEY = 'sk_test_mock_key';
process.env.STRIPE_WEBHOOK_SECRET = 'whsec_mock_secret';

describe('StripeWebhookService', () => {
  let service: StripeWebhookService;

  const mockSubscriptionRepository = {
    findByStripeSubscriptionId: jest.fn(),
    create: jest.fn(),
    updateByStripeSubscriptionId: jest.fn(),
    deleteByStripeSubscriptionId: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        StripeWebhookService,
        {
          provide: SubscriptionRepository,
          useValue: mockSubscriptionRepository,
        },
      ],
    }).compile();

    service = module.get<StripeWebhookService>(StripeWebhookService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('constructEvent', () => {
    it('should be defined', () => {
      expect(service.constructEvent).toBeDefined();
    });

    it('should throw error with invalid signature', () => {
      const rawBody = Buffer.from('test');
      const signature = 'invalid_signature';

      expect(() => {
        service.constructEvent(rawBody, signature);
      }).toThrow();
    });
  });

  describe('processEvent', () => {
    it('should be defined', () => {
      expect(service.processEvent).toBeDefined();
    });

    it('should handle unknown event types gracefully', async () => {
      const mockEvent = {
        id: 'evt_test',
        type: 'unknown.event.type',
        data: { object: {} },
      } as any;

      // Should not throw error for unknown event types
      await expect(service.processEvent(mockEvent)).resolves.not.toThrow();
    });
  });
});
