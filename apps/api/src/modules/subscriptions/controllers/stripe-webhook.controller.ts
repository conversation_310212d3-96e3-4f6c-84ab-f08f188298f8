import {
  Controller,
  Post,
  Req,
  <PERSON>s,
  Headers,
  HttpCode,
  HttpStatus,
  Logger,
  BadRequestException,
  InternalServerErrorException,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiHeader } from '@nestjs/swagger';
import { Request, Response } from 'express';
import { StripeWebhookService } from '../services/stripe-webhook.service';
import { ApiSurface } from '../../../common/decorators/api-surface.decorator';

// Extend Express Request to include rawBody
interface RawBodyRequest extends Request {
  rawBody?: Buffer;
}

@ApiTags('Stripe Webhooks')
@Controller('webhooks/stripe')
export class StripeWebhookController {
  private readonly logger = new Logger(StripeWebhookController.name);

  constructor(private readonly stripeWebhookService: StripeWebhookService) {}

  @Post()
  @HttpCode(HttpStatus.OK)
  @ApiSurface('external')
  @ApiOperation({
    summary: 'Handle Stripe webhook events',
    description: 'Endpoint for receiving and processing Stripe webhook events',
  })
  @ApiHeader({
    name: 'stripe-signature',
    description: 'Stripe webhook signature for verification',
    required: true,
  })
  @ApiResponse({
    status: 200,
    description: 'Webhook processed successfully',
    schema: {
      type: 'object',
      properties: {
        received: {
          type: 'boolean',
          example: true,
        },
      },
    },
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - invalid signature or payload',
    schema: {
      type: 'object',
      properties: {
        error: {
          type: 'string',
          example: 'Invalid webhook signature',
        },
      },
    },
  })
  @ApiResponse({
    status: 500,
    description: 'Internal server error',
    schema: {
      type: 'object',
      properties: {
        error: {
          type: 'string',
          example: 'Failed to process webhook event',
        },
      },
    },
  })
  async handleWebhook(
    @Req() req: RawBodyRequest,
    @Res() res: Response,
    @Headers('stripe-signature') signature: string,
  ): Promise<void> {
    const startTime = Date.now();
    this.logger.log('Received Stripe webhook request');

    try {
      // Validate signature header
      if (!signature) {
        this.logger.error('Missing stripe-signature header');
        throw new BadRequestException('Missing stripe-signature header');
      }

      // Get raw body - this should be set by middleware
      const rawBody = req.rawBody;
      if (!rawBody) {
        this.logger.error(
          'Missing raw body - ensure raw body middleware is configured',
        );
        throw new BadRequestException('Missing request body');
      }

      this.logger.debug(`Raw body length: ${rawBody.length} bytes`);

      // Construct and verify the webhook event
      let event;
      try {
        event = this.stripeWebhookService.constructEvent(rawBody, signature);
        this.logger.log(`Verified webhook event: ${event.type} (${event.id})`);
      } catch (error) {
        this.logger.error('Webhook signature verification failed', error);
        throw new BadRequestException(
          `Webhook signature verification failed: ${error.message}`,
        );
      }

      // Process the event
      try {
        await this.stripeWebhookService.processEvent(event);

        const processingTime = Date.now() - startTime;
        this.logger.log(
          `Successfully processed webhook event ${event.type} (${event.id}) in ${processingTime}ms`,
        );

        // Return success response
        res.status(HttpStatus.OK).json({ received: true });
      } catch (error) {
        this.logger.error(
          `Failed to process webhook event ${event.type} (${event.id})`,
          error,
        );
        throw new InternalServerErrorException(
          `Failed to process webhook event: ${error.message}`,
        );
      }
    } catch (error) {
      const processingTime = Date.now() - startTime;

      if (error instanceof BadRequestException) {
        this.logger.warn(
          `Bad request after ${processingTime}ms: ${error.message}`,
        );
        res.status(HttpStatus.BAD_REQUEST).json({ error: error.message });
      } else if (error instanceof InternalServerErrorException) {
        this.logger.error(
          `Internal server error after ${processingTime}ms: ${error.message}`,
        );
        res
          .status(HttpStatus.INTERNAL_SERVER_ERROR)
          .json({ error: error.message });
      } else {
        this.logger.error(`Unexpected error after ${processingTime}ms`, error);
        res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
          error: 'An unexpected error occurred while processing the webhook',
        });
      }
    }
  }

  /**
   * Health check endpoint for webhook endpoint
   */
  @Post('health')
  @HttpCode(HttpStatus.OK)
  @ApiSurface('external')
  @ApiOperation({
    summary: 'Webhook endpoint health check',
    description: 'Simple health check for the webhook endpoint',
  })
  @ApiResponse({
    status: 200,
    description: 'Webhook endpoint is healthy',
    schema: {
      type: 'object',
      properties: {
        status: {
          type: 'string',
          example: 'healthy',
        },
        timestamp: {
          type: 'string',
          format: 'date-time',
        },
      },
    },
  })
  async healthCheck(@Res() res: Response): Promise<void> {
    this.logger.debug('Webhook health check requested');

    res.status(HttpStatus.OK).json({
      status: 'healthy',
      timestamp: new Date().toISOString(),
    });
  }
}
