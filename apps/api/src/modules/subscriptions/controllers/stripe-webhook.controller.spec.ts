import { Test, TestingModule } from '@nestjs/testing';
import { StripeWebhookController } from './stripe-webhook.controller';
import { StripeWebhookService } from '../services/stripe-webhook.service';
import { SubscriptionRepository } from '@askinfosec/database-drizzle';

describe('StripeWebhookController', () => {
  let controller: StripeWebhookController;
  let service: StripeWebhookService;

  const mockStripeWebhookService = {
    constructEvent: jest.fn(),
    processEvent: jest.fn(),
  };

  const mockSubscriptionRepository = {
    findByStripeSubscriptionId: jest.fn(),
    create: jest.fn(),
    updateByStripeSubscriptionId: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [StripeWebhookController],
      providers: [
        {
          provide: StripeWebhookService,
          useValue: mockStripeWebhookService,
        },
        {
          provide: SubscriptionRepository,
          useValue: mockSubscriptionRepository,
        },
      ],
    }).compile();

    controller = module.get<StripeWebhookController>(StripeWebhookController);
    service = module.get<StripeWebhookService>(StripeWebhookService);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  it('should have a webhook service', () => {
    expect(service).toBeDefined();
  });

  describe('handleWebhook', () => {
    it('should be defined', () => {
      expect(controller.handleWebhook).toBeDefined();
    });
  });

  describe('healthCheck', () => {
    it('should be defined', () => {
      expect(controller.healthCheck).toBeDefined();
    });
  });
});
