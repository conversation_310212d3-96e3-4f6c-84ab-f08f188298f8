import { Module } from '@nestjs/common';
import { SubscriptionsController } from './controllers/subscriptions.controller';
import { StripeWebhookController } from './controllers/stripe-webhook.controller';
import { SubscriptionsService } from './services/subscriptions.service';
import { StripeWebhookService } from './services/stripe-webhook.service';
import { DatabaseDrizzleModule } from '../db-drizzle';

@Module({
  imports: [DatabaseDrizzleModule],
  controllers: [SubscriptionsController, StripeWebhookController],
  providers: [SubscriptionsService, StripeWebhookService],
  exports: [SubscriptionsService, StripeWebhookService],
})
export class SubscriptionsModule {}
