import { Injectable, NestMiddleware, Logger } from '@nestjs/common';
import { Request, Response, NextFunction } from 'express';

// Extend Express Request to include rawBody
interface RawBodyRequest extends Request {
  rawBody?: Buffer;
}

@Injectable()
export class RawBodyMiddleware implements NestMiddleware {
  private readonly logger = new Logger(RawBodyMiddleware.name);

  use(req: RawBodyRequest, res: Response, next: NextFunction): void {
    // Only process raw body for webhook endpoints
    if (req.path.includes('/webhooks/stripe')) {
      this.logger.debug(`Processing raw body for webhook: ${req.path}`);

      const chunks: Buffer[] = [];

      req.on('data', (chunk: Buffer) => {
        chunks.push(chunk);
      });

      req.on('end', () => {
        req.rawBody = Buffer.concat(chunks);
        this.logger.debug(`Raw body captured: ${req.rawBody.length} bytes`);
        next();
      });

      req.on('error', (error) => {
        this.logger.error('Error reading raw body', error);
        next(error);
      });
    } else {
      // For non-webhook endpoints, proceed normally
      next();
    }
  }
}
