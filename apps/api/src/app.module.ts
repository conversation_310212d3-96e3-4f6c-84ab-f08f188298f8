import { APP_GUARD } from '@nestjs/core';
import { MiddlewareConsumer, Module, NestModule } from '@nestjs/common';
import { AuthModule } from './modules/auth/auth.module';
import { RequestLoggingMiddleware } from './middleware/request-logging.middleware';
import { RawBodyMiddleware } from './middleware/raw-body.middleware';
import { SessionExtractionMiddleware } from './modules/db-drizzle/middleware/session-extraction.middleware';
import { ApiSurfaceGuard } from './common/guards/api-surface.guard';
import { DatabaseDrizzleModule } from './modules/db-drizzle';
import { OrganizationsModule } from './modules/organizations/organizations.module';
import { UsersModule } from './modules/users/users.module';
import { ResendModule } from './modules/resend';
import { ProductsModule } from './modules/products/products.module';
import { SystemModulesModule } from './modules/system-modules/system-modules.module';
import { OrganizationModulesModule } from './modules/organization-modules/organization-modules.module';
import { MembersModule } from './modules/members';
import { SubscriptionsModule } from './modules/subscriptions/subscriptions.module';

@Module({
  imports: [
    AuthModule,
    DatabaseDrizzleModule,
    OrganizationsModule,
    UsersModule,
    ResendModule.forRoot(),
    ProductsModule,
    SystemModulesModule,
    OrganizationModulesModule,
    MembersModule,
    SubscriptionsModule,
  ],
  controllers: [],
  providers: [
    {
      provide: APP_GUARD,
      useClass: ApiSurfaceGuard,
    },
  ],
})
export class AppModule implements NestModule {
  configure(consumer: MiddlewareConsumer): void {
    // Apply raw body middleware first for webhook endpoints
    consumer.apply(RawBodyMiddleware).forRoutes('*');

    // Apply other middleware
    consumer
      .apply(SessionExtractionMiddleware, RequestLoggingMiddleware)
      .forRoutes('*');
  }
}
