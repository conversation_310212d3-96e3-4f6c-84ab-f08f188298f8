# Stripe Webhooks Implementation

This document describes the Stripe webhook implementation for the AskInfosec NestJS API.

## Overview

The Stripe webhook system handles real-time events from Stripe, such as subscription changes, payment updates, and customer modifications. This implementation follows NestJS best practices and integrates with the existing database architecture.

## Architecture

### Components

1. **StripeWebhookController** (`apps/api/src/modules/subscriptions/controllers/stripe-webhook.controller.ts`)
   - Handles incoming webhook requests
   - Validates webhook signatures
   - Returns appropriate HTTP responses

2. **StripeWebhookService** (`apps/api/src/modules/subscriptions/services/stripe-webhook.service.ts`)
   - Processes webhook events
   - Contains business logic for different event types
   - Integrates with repository layer

3. **SubscriptionRepository** (`packages/database-drizzle/src/repositories/subscription.repository.ts`)
   - Data access layer for subscription operations
   - Currently a stub - needs schema implementation

4. **RawBodyMiddleware** (`apps/api/src/middleware/raw-body.middleware.ts`)
   - Captures raw request body for webhook signature verification
   - Only processes webhook endpoints

## Configuration

### Environment Variables

The following environment variables are required:

```bash
# Stripe Configuration
STRIPE_SECRET_KEY=sk_test_...          # Stripe secret key
STRIPE_WEBHOOK_SECRET=whsec_...        # Webhook endpoint secret from Stripe dashboard
```

### Webhook Endpoint

The webhook endpoint is available at:
```
POST /v1/webhooks/stripe
```

### Supported Events

Currently configured to handle the following Stripe events:

- `customer.subscription.created`
- `customer.subscription.updated`
- `customer.subscription.deleted`
- `invoice.payment_succeeded`
- `invoice.payment_failed`
- `customer.created`
- `customer.updated`
- `payment_intent.succeeded`
- `payment_intent.payment_failed`

## Setup Instructions

### 1. Install Dependencies

The required dependencies are already added to `package.json`:
- `stripe`: Stripe Node.js library
- `@nestjs/swagger`: API documentation

### 2. Configure Stripe Dashboard

1. Log into your Stripe Dashboard
2. Go to Developers > Webhooks
3. Create a new webhook endpoint
4. Set the endpoint URL to: `https://your-api-domain.com/v1/webhooks/stripe`
5. Select the events you want to receive
6. Copy the webhook signing secret

### 3. Set Environment Variables

Add the required environment variables to your `.env` file:

```bash
STRIPE_SECRET_KEY=your_stripe_secret_key
STRIPE_WEBHOOK_SECRET=your_webhook_signing_secret
```

## Implementation Status

### ✅ Completed

- [x] Webhook controller with signature verification
- [x] Webhook service with event routing
- [x] Raw body middleware for signature verification
- [x] Basic error handling and logging
- [x] Health check endpoint
- [x] Test files structure
- [x] Documentation

### 🚧 Stub Implementation (Needs Future Work)

- [ ] **SubscriptionRepository**: Currently contains stub methods that throw errors
- [ ] **Database Schema**: Subscription tables need to be defined in Drizzle schema
- [ ] **Event Handlers**: All event handlers are stubs that log warnings
- [ ] **Business Logic**: Actual subscription management logic needs implementation

## Testing

### Unit Tests

Run the tests with:
```bash
pnpm --filter api test
```

### Manual Testing

1. **Health Check**:
   ```bash
   curl -X POST http://localhost:5005/v1/webhooks/stripe/health
   ```

2. **Webhook Testing**: Use Stripe CLI for local testing:
   ```bash
   stripe listen --forward-to localhost:5005/v1/webhooks/stripe
   ```

## Security Considerations

1. **Signature Verification**: All webhook requests are verified using Stripe's signature
2. **Raw Body Processing**: Raw body is only processed for webhook endpoints
3. **Error Handling**: Sensitive information is not exposed in error responses
4. **Logging**: Comprehensive logging for debugging and monitoring

## Future Implementation Tasks

### High Priority

1. **Implement Database Schema**:
   - Define subscription tables in Drizzle schema
   - Add proper TypeScript types
   - Implement repository methods

2. **Implement Event Handlers**:
   - Subscription lifecycle management
   - Payment processing logic
   - Customer data synchronization

### Medium Priority

3. **Enhanced Error Handling**:
   - Retry mechanisms for failed events
   - Dead letter queue for problematic events
   - Monitoring and alerting

4. **Performance Optimization**:
   - Batch processing for multiple events
   - Caching for frequently accessed data

### Low Priority

5. **Advanced Features**:
   - Webhook event replay functionality
   - Custom event filtering
   - Webhook analytics and reporting

## API Documentation

When the API is running, Swagger documentation is available at:
```
http://localhost:5005/api-docs
```

The webhook endpoints are documented with proper request/response schemas and examples.

## Troubleshooting

### Common Issues

1. **Missing Signature Header**: Ensure Stripe is configured to send the `stripe-signature` header
2. **Invalid Signature**: Verify the webhook secret matches the one in Stripe dashboard
3. **Raw Body Issues**: Ensure the raw body middleware is properly configured
4. **Environment Variables**: Check that all required environment variables are set

### Debugging

Enable debug logging by setting the log level:
```bash
LOG_LEVEL=debug
```

Check the application logs for detailed webhook processing information.
